#!/usr/bin/env python3
"""
Test script for the document upload endpoint.
This script demonstrates how to upload files to the /process-documents-upload endpoint.
"""

import requests
import os
from pathlib import Path

# API configuration
API_BASE_URL = "http://localhost:8000"  # Change this to your server URL
UPLOAD_ENDPOINT = f"{API_BASE_URL}/process-documents-upload"

def test_upload_endpoint():
    """Test the document upload endpoint with sample files."""
    
    # Test parameters
    collection_name = "test_upload_collection"
    upload_directory = "data/uploaded_docs"  # Directory where files will be uploaded
    recursive = True
    
    # Create a test file if it doesn't exist
    test_file_path = Path("test_document.txt")
    if not test_file_path.exists():
        with open(test_file_path, "w", encoding="utf-8") as f:
            f.write("""Bu bir test dokümandır.
            
Bu doküman, dosya yükleme endpoint'ini test etmek için oluşturulmuştur.
Türkçe karakterler: ğ<PERSON>şıöç ĞÜŞIÖÇ

Test içeriği:
- Madde 1: API endpoint testi
- Madde 2: Dosya yükleme işlemi
- Madde 3: Doküman işleme süreci

Bu test başarılı olursa, dosya yükleme sistemi çalışıyor demektir.
""")
        print(f"Created test file: {test_file_path}")
    
    try:
        # Prepare files for upload
        files = []
        file_paths = [test_file_path]  # Add more files here if needed
        
        for file_path in file_paths:
            if Path(file_path).exists():
                files.append(('files', (file_path.name, open(file_path, 'rb'), 'text/plain')))
            else:
                print(f"Warning: File not found: {file_path}")
        
        if not files:
            print("No files to upload!")
            return
        
        # Prepare form data
        data = {
            'collection_name': collection_name,
            'upload_directory': upload_directory,
            'recursive': recursive
        }
        
        print(f"Uploading {len(files)} file(s) to {UPLOAD_ENDPOINT}")
        print(f"Collection: {collection_name}")
        print(f"Upload directory: {upload_directory}")
        print(f"Recursive: {recursive}")
        print("-" * 50)
        
        # Make the request
        response = requests.post(
            UPLOAD_ENDPOINT,
            files=files,
            data=data,
            timeout=300  # 5 minutes timeout
        )
        
        # Close file handles
        for _, file_tuple in files:
            file_tuple[1].close()
        
        # Check response
        if response.status_code == 200:
            result = response.json()
            print("✅ Upload successful!")
            print(f"Message: {result.get('message', 'No message')}")
            print(f"Uploaded files: {result.get('uploaded_files', [])}")
            print(f"Collection: {result.get('collection_name', 'N/A')}")
            print(f"Total files: {result.get('total_files', 0)}")
            print(f"Successful: {result.get('successful_count', 0)}")
            print(f"Failed: {result.get('failed_count', 0)}")
            print(f"Upload directory: {result.get('upload_directory', 'N/A')}")
            
            if result.get('upload_errors'):
                print(f"Upload errors: {result['upload_errors']}")
            
            if result.get('details'):
                print("Details available in response")
                
        else:
            print(f"❌ Upload failed with status code: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error: {error_data.get('detail', 'Unknown error')}")
            except:
                print(f"Response text: {response.text}")
    
    except requests.exceptions.ConnectionError:
        print("❌ Connection error! Make sure the API server is running.")
    except requests.exceptions.Timeout:
        print("❌ Request timeout! The upload took too long.")
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
    
    finally:
        # Clean up test file
        if test_file_path.exists():
            print(f"\nCleaning up test file: {test_file_path}")
            test_file_path.unlink()

def test_with_multiple_files():
    """Test with multiple files of different types."""
    
    # Create test files
    test_files = {
        "test1.txt": "Bu birinci test dosyasıdır.\nİçerik: Metin dosyası testi.",
        "test2.md": "# Test Markdown\n\nBu bir **markdown** dosyasıdır.\n\n- Liste item 1\n- Liste item 2",
    }
    
    created_files = []
    
    try:
        # Create test files
        for filename, content in test_files.items():
            file_path = Path(filename)
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            created_files.append(file_path)
            print(f"Created test file: {filename}")
        
        # Test parameters
        collection_name = "multi_file_test_collection"
        upload_directory = "data/multi_upload_test"
        
        # Prepare files for upload
        files = []
        for file_path in created_files:
            mime_type = "text/markdown" if file_path.suffix == ".md" else "text/plain"
            files.append(('files', (file_path.name, open(file_path, 'rb'), mime_type)))
        
        # Prepare form data
        data = {
            'collection_name': collection_name,
            'upload_directory': upload_directory,
            'recursive': True
        }
        
        print(f"\n{'='*60}")
        print("TESTING MULTIPLE FILES UPLOAD")
        print(f"{'='*60}")
        print(f"Uploading {len(files)} files...")
        
        # Make the request
        response = requests.post(
            UPLOAD_ENDPOINT,
            files=files,
            data=data,
            timeout=300
        )
        
        # Close file handles
        for _, file_tuple in files:
            file_tuple[1].close()
        
        # Process response
        if response.status_code == 200:
            result = response.json()
            print("✅ Multi-file upload successful!")
            print(f"Uploaded files: {result.get('uploaded_files', [])}")
            print(f"Processing results: {result.get('successful_count', 0)} successful, {result.get('failed_count', 0)} failed")
        else:
            print(f"❌ Multi-file upload failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error: {error_data.get('detail', 'Unknown error')}")
            except:
                print(f"Response: {response.text}")
    
    except Exception as e:
        print(f"❌ Error in multi-file test: {str(e)}")
    
    finally:
        # Clean up test files
        for file_path in created_files:
            if file_path.exists():
                file_path.unlink()
                print(f"Cleaned up: {file_path}")

if __name__ == "__main__":
    print("🚀 Testing Document Upload Endpoint")
    print("=" * 60)
    
    # Test 1: Single file upload
    print("TEST 1: Single file upload")
    test_upload_endpoint()
    
    # Test 2: Multiple files upload
    print("\nTEST 2: Multiple files upload")
    test_with_multiple_files()
    
    print("\n✅ All tests completed!")
