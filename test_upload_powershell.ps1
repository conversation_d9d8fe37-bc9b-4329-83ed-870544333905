# Test script for document upload endpoint using PowerShell
# This script demonstrates how to upload files using PowerShell

$API_URL = "http://localhost:8000"
$ENDPOINT = "/process-documents-upload"

Write-Host "🚀 Testing Document Upload Endpoint with PowerShell" -ForegroundColor Green
Write-Host "====================================================" -ForegroundColor Green

# Create a test file
$TEST_FILE = "test_upload_ps.txt"
$testContent = @"
Bu bir test dokümandır.

Bu doküman PowerShell ile dosya yükleme endpoint'ini test etmek için oluşturulmuştur.
Türkçe karakterler: ğüşıöç ĞÜŞIÖÇ

Test içeriği:
- API endpoint testi
- PowerShell ile dosya yükleme
- Doküman işleme süreci

Bu test başarılı olursa, PowerShell ile dosya yükleme sistemi ç<PERSON>ıyo<PERSON> demektir.
"@

$testContent | Out-File -FilePath $TEST_FILE -Encoding UTF8
Write-Host "✅ Test file created: $TEST_FILE" -ForegroundColor Green

# Test parameters
$COLLECTION_NAME = "powershell_test_collection"
$UPLOAD_DIRECTORY = "data/powershell_uploaded_docs"
$RECURSIVE = $true

Write-Host ""
Write-Host "📤 Uploading file with PowerShell..." -ForegroundColor Yellow
Write-Host "Collection: $COLLECTION_NAME"
Write-Host "Upload Directory: $UPLOAD_DIRECTORY"
Write-Host "File: $TEST_FILE"
Write-Host ""

try {
    # Prepare the multipart form data
    $boundary = [System.Guid]::NewGuid().ToString()
    $LF = "`r`n"
    
    # Read file content
    $fileBytes = [System.IO.File]::ReadAllBytes((Resolve-Path $TEST_FILE))
    $fileContent = [System.Text.Encoding]::GetEncoding("iso-8859-1").GetString($fileBytes)
    
    # Build multipart form data
    $bodyLines = @(
        "--$boundary",
        "Content-Disposition: form-data; name=`"collection_name`"",
        "",
        $COLLECTION_NAME,
        "--$boundary",
        "Content-Disposition: form-data; name=`"upload_directory`"",
        "",
        $UPLOAD_DIRECTORY,
        "--$boundary",
        "Content-Disposition: form-data; name=`"recursive`"",
        "",
        $RECURSIVE.ToString().ToLower(),
        "--$boundary",
        "Content-Disposition: form-data; name=`"files`"; filename=`"$TEST_FILE`"",
        "Content-Type: text/plain",
        "",
        $fileContent,
        "--$boundary--"
    )
    
    $body = $bodyLines -join $LF
    
    # Make the request
    $response = Invoke-RestMethod -Uri "$API_URL$ENDPOINT" -Method Post -Body $body -ContentType "multipart/form-data; boundary=$boundary"
    
    Write-Host "✅ Upload successful!" -ForegroundColor Green
    Write-Host "Message: $($response.message)"
    Write-Host "Uploaded files: $($response.uploaded_files -join ', ')"
    Write-Host "Collection: $($response.collection_name)"
    Write-Host "Total files: $($response.total_files)"
    Write-Host "Successful: $($response.successful_count)"
    Write-Host "Failed: $($response.failed_count)"
    Write-Host "Upload directory: $($response.upload_directory)"
    
    if ($response.upload_errors) {
        Write-Host "Upload errors: $($response.upload_errors -join ', ')" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "❌ Upload failed!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "HTTP Status: $statusCode" -ForegroundColor Red
    }
}
finally {
    # Clean up test file
    if (Test-Path $TEST_FILE) {
        Remove-Item $TEST_FILE
        Write-Host ""
        Write-Host "🧹 Cleaned up test file: $TEST_FILE" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "✅ Test completed!" -ForegroundColor Green

Write-Host ""
Write-Host "📝 Alternative PowerShell method using Invoke-WebRequest:" -ForegroundColor Cyan
Write-Host @"
`$form = @{
    collection_name = 'my_collection'
    upload_directory = 'data/my_uploads'
    recursive = 'true'
    files = Get-Item 'path/to/your/file.pdf'
}
Invoke-WebRequest -Uri '$API_URL$ENDPOINT' -Method Post -Form `$form
"@ -ForegroundColor Gray
