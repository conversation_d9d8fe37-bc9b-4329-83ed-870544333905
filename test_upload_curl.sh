#!/bin/bash

# Test script for document upload endpoint using curl
# This script demonstrates how to upload files using curl command

API_URL="http://localhost:8000"
ENDPOINT="/process-documents-upload"

echo "🚀 Testing Document Upload Endpoint with curl"
echo "=============================================="

# Create a test file
TEST_FILE="test_upload.txt"
cat > "$TEST_FILE" << 'EOF'
Bu bir test dokümandır.

Bu doküman curl ile dosya yükleme endpoint'ini test etmek için oluşturulmuştur.
Türkçe karakterler: ğüşıöç ĞÜŞIÖÇ

Test içeriği:
- API endpoint testi
- Curl ile dosya yükleme
- Doküman işleme süreci

Bu test başarılı olursa, curl ile dosya yükleme sistemi çalışıyor demektir.
EOF

echo "✅ Test file created: $TEST_FILE"

# Test parameters
COLLECTION_NAME="curl_test_collection"
UPLOAD_DIRECTORY="data/curl_uploaded_docs"
RECURSIVE="true"

echo ""
echo "📤 Uploading file with curl..."
echo "Collection: $COLLECTION_NAME"
echo "Upload Directory: $UPLOAD_DIRECTORY"
echo "File: $TEST_FILE"
echo ""

# Make the curl request
curl -X POST "$API_URL$ENDPOINT" \
  -F "collection_name=$COLLECTION_NAME" \
  -F "upload_directory=$UPLOAD_DIRECTORY" \
  -F "recursive=$RECURSIVE" \
  -F "files=@$TEST_FILE" \
  -H "Accept: application/json" \
  -w "\n\nHTTP Status: %{http_code}\nTotal Time: %{time_total}s\n" \
  -s

echo ""
echo "🧹 Cleaning up test file..."
rm -f "$TEST_FILE"
echo "✅ Test completed!"

echo ""
echo "📝 Alternative curl command for multiple files:"
echo "curl -X POST \"$API_URL$ENDPOINT\" \\"
echo "  -F \"collection_name=my_collection\" \\"
echo "  -F \"upload_directory=data/my_uploads\" \\"
echo "  -F \"recursive=true\" \\"
echo "  -F \"files=@file1.pdf\" \\"
echo "  -F \"files=@file2.docx\" \\"
echo "  -F \"files=@file3.txt\" \\"
echo "  -H \"Accept: application/json\""
