# Document Upload API Endpoint

B<PERSON>, dı<PERSON><PERSON><PERSON><PERSON> dosya yükleme için o<PERSON> `/process-documents-upload` endpoint'inin kullan<PERSON>mını açıklar.

## Endpoint Bilgileri

- **URL**: `/process-documents-upload`
- **Method**: `POST`
- **Content-Type**: `multipart/form-data`
- **Tags**: `Documents`

## Parametreler

### Form Data Parametreleri

| Parametre | Tip | Zorunlu | Açıklama |
|-----------|-----|---------|----------|
| `collection_name` | string | ✅ | Dokümanların saklanacağı koleksiyon adı |
| `upload_directory` | string | ✅ | Dosyaların yükleneceği dizin yolu |
| `recursive` | boolean | ❌ | Alt dizinleri de işleyip işlemeyeceği (varsayılan: true) |
| `files` | file[] | ✅ | <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dos<PERSON> (birden fazla dosya desteklenir) |

### Desteklenen Dosya Formatları

- `.pdf` - PDF dokümanları
- `.docx` - Word dokümanları (yeni format)
- `.doc` - Word dokümanları (eski format)
- `.txt` - Metin dosyaları
- `.md` - Markdown dosyaları

## Response Formatı

### Başarılı Response (200 OK)

```json
{
  "success": true,
  "message": "Successfully uploaded and processed 2 files",
  "uploaded_files": ["document1.pdf", "document2.txt"],
  "collection_name": "my_collection",
  "total_files": 2,
  "successful_count": 2,
  "failed_count": 0,
  "upload_directory": "data/my_uploads",
  "upload_errors": null,
  "details": {
    "processing_results": [...],
    "upload_errors": null
  }
}
```

### Hata Response (400/500)

```json
{
  "success": false,
  "message": "File upload failed",
  "error": "No files were successfully uploaded",
  "upload_errors": ["Unsupported file type: image.jpg (.jpg)"]
}
```

## Kullanım Örnekleri

### 1. Python ile Requests Kullanarak

```python
import requests

url = "http://localhost:8000/process-documents-upload"

# Form data
data = {
    'collection_name': 'my_documents',
    'upload_directory': 'data/uploaded_docs',
    'recursive': True
}

# Files to upload
files = [
    ('files', ('document1.pdf', open('document1.pdf', 'rb'), 'application/pdf')),
    ('files', ('document2.txt', open('document2.txt', 'rb'), 'text/plain'))
]

response = requests.post(url, files=files, data=data)
print(response.json())

# Don't forget to close files
for _, file_tuple in files:
    file_tuple[1].close()
```

### 2. cURL ile

```bash
curl -X POST "http://localhost:8000/process-documents-upload" \
  -F "collection_name=my_collection" \
  -F "upload_directory=data/my_uploads" \
  -F "recursive=true" \
  -F "files=@document1.pdf" \
  -F "files=@document2.txt" \
  -H "Accept: application/json"
```

### 3. PowerShell ile

```powershell
$form = @{
    collection_name = 'my_collection'
    upload_directory = 'data/my_uploads'
    recursive = 'true'
    files = Get-Item 'document1.pdf'
}

Invoke-WebRequest -Uri 'http://localhost:8000/process-documents-upload' -Method Post -Form $form
```

### 4. JavaScript/Fetch ile

```javascript
const formData = new FormData();
formData.append('collection_name', 'my_collection');
formData.append('upload_directory', 'data/my_uploads');
formData.append('recursive', 'true');
formData.append('files', fileInput.files[0]); // HTML file input
formData.append('files', fileInput.files[1]); // Multiple files

fetch('http://localhost:8000/process-documents-upload', {
    method: 'POST',
    body: formData
})
.then(response => response.json())
.then(data => console.log(data));
```

## Özellikler

### 1. Çoklu Dosya Desteği
- Tek istekte birden fazla dosya yükleyebilirsiniz
- Her dosya ayrı ayrı işlenir ve sonuçlar raporlanır

### 2. Dosya Adı Çakışması Yönetimi
- Aynı isimde dosyalar varsa otomatik olarak `_1`, `_2` gibi sayılar eklenir
- Örnek: `document.pdf` → `document_1.pdf`

### 3. Hata Yönetimi
- Desteklenmeyen dosya formatları reddedilir
- Her dosya için ayrı hata raporlaması
- Kısmi başarı durumları desteklenir (bazı dosyalar başarılı, bazıları başarısız)

### 4. Metadata Ekleme
- Yüklenen dosyalara otomatik metadata eklenir:
  - `source_type`: "external_upload"
  - `uploaded_via`: "api_endpoint"
  - `upload_timestamp`: Yükleme zamanı
  - `original_filenames`: Orijinal dosya isimleri

### 5. Dizin Oluşturma
- Belirtilen upload dizini yoksa otomatik olarak oluşturulur
- Recursive işleme desteği

## Docker ile Kullanım

Docker container'da çalışırken, dosyaların container içinde erişilebilir bir yere yüklenmesi gerekir. Volume mount kullanarak host sistemdeki bir dizini container'a bağlayabilirsiniz:

```bash
docker run -v /host/path/data:/app/data -p 8000:8000 your-app-image
```

Bu durumda `upload_directory` parametresi olarak `/app/data/uploads` gibi container içindeki bir path kullanın.

## Test Scriptleri

Proje ile birlikte aşağıdaki test scriptleri sağlanmıştır:

- `test_upload_endpoint.py` - Python ile test
- `test_upload_curl.sh` - Bash/cURL ile test
- `test_upload_powershell.ps1` - PowerShell ile test

Bu scriptleri çalıştırarak endpoint'in doğru çalıştığını doğrulayabilirsiniz.

## Güvenlik Notları

1. **Dosya Boyutu Limiti**: FastAPI'nin varsayılan dosya boyutu limitlerini göz önünde bulundurun
2. **Dosya Türü Kontrolü**: Sadece desteklenen dosya türleri kabul edilir
3. **Dizin Güvenliği**: Upload dizininin güvenli bir konumda olduğundan emin olun
4. **Rate Limiting**: Yoğun kullanım için rate limiting eklemeyi düşünün

## Sorun Giderme

### Yaygın Hatalar

1. **"No files were successfully uploaded"**
   - Dosya formatının desteklendiğinden emin olun
   - Dosya boyutunun uygun olduğunu kontrol edin

2. **"Connection error"**
   - API sunucusunun çalıştığından emin olun
   - URL'nin doğru olduğunu kontrol edin

3. **"Permission denied"**
   - Upload dizinine yazma izni olduğundan emin olun
   - Docker kullanıyorsanız volume mount'ların doğru olduğunu kontrol edin

### Log Kontrolü

API sunucusunun loglarını kontrol ederek detaylı hata bilgilerine ulaşabilirsiniz:

```bash
# Docker logs
docker logs container-name

# Local development
tail -f logs/api.log
```
